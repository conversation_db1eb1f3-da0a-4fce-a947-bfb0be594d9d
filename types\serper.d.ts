export interface SerperSearchRequest {
  q: string;
  num?: number;
  gl?: string;
  hl?: string;
}

export interface SerperSearchResult {
  title: string;
  link: string;
  snippet: string;
  position: number;
  date?: string;
}

export interface SerperResponse {
  organic: SerperSearchResult[];
  searchParameters: {
    q: string;
    num: number;
    gl: string;
    hl: string;
  };
  searchInformation: {
    totalResults: string;
    timeTaken: number;
  };
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

'use client';

import { useState } from 'react';
import { SearchForm } from '@/components/serper/search-form';
import { SearchResults } from '@/components/serper/search-results';
import { SerperSearchRequest, SerperResponse, ApiResponse } from '@/types/serper';
import { toast } from 'sonner';

export default function SerperPage() {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<SerperResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleSearch = async (params: SerperSearchRequest) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/serper', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });

      const result: ApiResponse<SerperResponse> = await response.json();

      if (!result.success) {
        throw new Error(result.error || '搜索失败');
      }

      setData(result.data || null);
      toast.success(`搜索完成，找到 ${result.data?.organic.length || 0} 条结果`);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '搜索请求失败';
      setError(errorMessage);
      toast.error(errorMessage);
      setData(null);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">搜索工具</h1>
        <p className="text-muted-foreground">
          基于 Google Serper API 的智能搜索工具
        </p>
      </div>

      <SearchForm onSearch={handleSearch} loading={loading} />
      
      <SearchResults data={data} loading={loading} error={error} />
    </div>
  );
}

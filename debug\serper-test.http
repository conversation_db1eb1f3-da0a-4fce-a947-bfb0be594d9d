### Test Serper API
POST http://localhost:3000/api/serper
Content-Type: application/json

{
  "q": "Next.js tutorial",
  "num": 10
}

### Test with more parameters
POST http://localhost:3000/api/serper
Content-Type: application/json

{
  "q": "React hooks",
  "num": 20,
  "gl": "us",
  "hl": "en"
}

### Test empty query (should fail)
POST http://localhost:3000/api/serper
Content-Type: application/json

{
  "q": "",
  "num": 10
}

### Test GET method (should return info)
GET http://localhost:3000/api/serper

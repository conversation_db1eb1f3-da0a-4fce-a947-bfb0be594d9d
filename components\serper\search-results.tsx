'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ExternalLink, Clock, Search } from 'lucide-react';
import { SerperResponse } from '@/types/serper';

interface SearchResultsProps {
  data: SerperResponse | null;
  loading: boolean;
  error: string | null;
}

export function SearchResults({ data, loading, error }: SearchResultsProps) {
  if (loading) {
    return (
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="flex items-center justify-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            <span>正在搜索...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full border-destructive">
        <CardContent className="p-6">
          <div className="text-center text-destructive">
            <p className="font-medium">搜索失败</p>
            <p className="text-sm mt-1">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>请输入关键词开始搜索</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const {
    organic = [],
    searchParameters,
    searchInformation
  } = data;

  return (
    <div className="space-y-4">
      {/* 搜索信息 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">搜索结果</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Search className="h-4 w-4" />
              关键词: <span className="font-medium">{searchParameters?.q || '未知'}</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              用时: <span className="font-medium">{searchInformation?.timeTaken || 0}秒</span>
            </div>
            <div>
              总结果: <span className="font-medium">{searchInformation?.totalResults || '0'}</span>
            </div>
            <div>
              显示: <span className="font-medium">{organic.length}</span> 条
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 搜索结果列表 */}
      <div className="space-y-3">
        {organic.map((result, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="space-y-2">
                <div className="flex items-start justify-between gap-2">
                  <h3 className="font-medium text-lg leading-tight hover:text-primary">
                    <a 
                      href={result.link} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="flex items-start gap-2 group"
                    >
                      <span className="flex-1">{result.title}</span>
                      <ExternalLink className="h-4 w-4 mt-1 opacity-50 group-hover:opacity-100 flex-shrink-0" />
                    </a>
                  </h3>
                  <Badge variant="secondary" className="flex-shrink-0">
                    #{result.position}
                  </Badge>
                </div>
                
                <p className="text-sm text-muted-foreground line-clamp-3">
                  {result.snippet}
                </p>
                
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <a 
                    href={result.link} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="hover:text-primary truncate max-w-md"
                  >
                    {result.link}
                  </a>
                  {result.date && (
                    <span className="flex-shrink-0">{result.date}</span>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {organic.length === 0 && (
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-muted-foreground">
              <p>没有找到相关结果</p>
              <p className="text-sm mt-1">请尝试使用其他关键词</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

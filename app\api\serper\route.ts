import { NextRequest, NextResponse } from 'next/server';
import { SerperSearchRequest, SerperResponse, ApiResponse } from '@/types/serper';

const SERPER_API_KEY = 'e60de2c2488f3a58c02f5e917b1f00ee48c796ef';
const SERPER_API_URL = 'https://google.serper.dev/search';

export async function POST(request: NextRequest) {
  try {
    const body: SerperSearchRequest = await request.json();
    
    // 验证必需参数
    if (!body.q || body.q.trim() === '') {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: '搜索关键词不能为空'
      }, { status: 400 });
    }

    // 构建请求参数
    const searchParams = {
      q: body.q.trim(),
      num: body.num || 20,
      gl: body.gl || 'cn',
      hl: body.hl || 'zh-cn'
    };

    // 调用Serper API
    const response = await fetch(SERPER_API_URL, {
      method: 'POST',
      headers: {
        'X-API-KEY': SERPER_API_KEY,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(searchParams)
    });

    if (!response.ok) {
      throw new Error(`Serper API error: ${response.status} ${response.statusText}`);
    }

    const data: SerperResponse = await response.json();

    return NextResponse.json<ApiResponse<SerperResponse>>({
      success: true,
      data
    });

  } catch (error) {
    console.error('Serper API error:', error);
    
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: error instanceof Error ? error.message : '搜索请求失败'
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Serper Search API',
    methods: ['POST'],
    description: '使用POST方法发送搜索请求'
  });
}
